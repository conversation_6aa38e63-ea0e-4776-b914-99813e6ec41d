<template>
  <a-breadcrumb class="breadcrumb">
    <a-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="index">
      <router-link :to="item.path" v-if="item.path && index < breadcrumbItems.length - 1">
        {{ item.title }}
      </router-link>
      <span v-else>{{ item.title }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { useRoute, RouteLocationNormalizedLoaded } from 'vue-router';

interface BreadcrumbItem {
  title: string;
  path: string;
}

const route = useRoute();
const breadcrumbItems = ref<BreadcrumbItem[]>([]);

// 根据路由生成面包屑
const generateBreadcrumb = (route: RouteLocationNormalizedLoaded) => {
  const items: BreadcrumbItem[] = [];

  // 添加首页
  items.push({
    title: '首页',
    path: '/',
  });

  // 根据当前路由匹配的路径生成面包屑
  if (route.name && route.meta.title) {
    items.push({
      title: route.meta.title as string,
      path: route.path,
    });
  }

  // 如果有额外的路径参数，添加到面包屑
  if (route.params.id) {
    const detailTitle = `详情 ${route.params.id}`;
    items.push({
      title: detailTitle,
      path: '',
    });
  }

  return items;
};

// 监听路由变化，更新面包屑
watch(
  () => route.path,
  () => {
    breadcrumbItems.value = generateBreadcrumb(route);
  },
  { immediate: true }
);
</script>

<style lang="less" scoped>
.breadcrumb {
  margin-left: 16px;
}
</style>
