<template>
  <div class="dashboard-container">
    <a-row :gutter="[16, 16]">
      <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <a-card title="平台概览" :bordered="false">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="数据集" :value="statistics.datasets" :loading="loading">
                <template #prefix>
                  <DatabaseOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic title="训练任务" :value="statistics.trainingJobs" :loading="loading">
                <template #prefix>
                  <ExperimentOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic title="模型" :value="statistics.models" :loading="loading">
                <template #prefix>
                  <AppstoreOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic title="已部署模型" :value="statistics.deployments" :loading="loading">
                <template #prefix>
                  <CloudServerOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <a-card title="最近训练任务" :bordered="false">
          <a-table
            :dataSource="recentTrainingJobs"
            :columns="trainingColumns"
            :pagination="false"
            :loading="loading"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ record.status }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" @click="viewTrainingJob(record.id)"> 查看 </a-button>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <a-card title="系统状态" :bordered="false">
          <a-row :gutter="[0, 16]">
            <a-col :span="24">
              <a-progress :percent="systemStatus.cpuUsage" :stroke-color="getResourceColor(systemStatus.cpuUsage)" size="small" />
              <div class="resource-label">CPU 使用率: {{ systemStatus.cpuUsage }}%</div>
            </a-col>
            <a-col :span="24">
              <a-progress
                :percent="systemStatus.memoryUsage"
                :stroke-color="getResourceColor(systemStatus.memoryUsage)"
                size="small"
              />
              <div class="resource-label">内存使用率: {{ systemStatus.memoryUsage }}%</div>
            </a-col>
            <a-col :span="24">
              <a-progress
                :percent="systemStatus.diskUsage"
                :stroke-color="getResourceColor(systemStatus.diskUsage)"
                size="small"
              />
              <div class="resource-label">磁盘使用率: {{ systemStatus.diskUsage }}%</div>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { DatabaseOutlined, ExperimentOutlined, AppstoreOutlined, CloudServerOutlined } from '@ant-design/icons-vue';

const router = useRouter();
const loading = ref(true);

// 模拟数据
const statistics = reactive({
  datasets: 0,
  trainingJobs: 0,
  models: 0,
  deployments: 0,
});

const systemStatus = reactive({
  cpuUsage: 0,
  memoryUsage: 0,
  diskUsage: 0,
});

const recentTrainingJobs = ref([]);

const trainingColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '模型类型',
    dataIndex: 'modelType',
    key: 'modelType',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
  },
];

const getStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    COMPLETED: 'success',
    RUNNING: 'processing',
    FAILED: 'error',
    PENDING: 'warning',
    STOPPED: 'default',
  };
  return statusMap[status] || 'default';
};

const getResourceColor = (usage: number) => {
  if (usage >= 90) return '#f5222d';
  if (usage >= 70) return '#faad14';
  return '#52c41a';
};

const viewTrainingJob = (id: string) => {
  router.push(`/training/${id}`);
};

// 模拟加载数据
onMounted(async () => {
  setTimeout(() => {
    statistics.datasets = 24;
    statistics.trainingJobs = 156;
    statistics.models = 42;
    statistics.deployments = 18;

    systemStatus.cpuUsage = 45;
    systemStatus.memoryUsage = 62;
    systemStatus.diskUsage = 78;

    recentTrainingJobs.value = [
      { id: '1', name: '科创健康性模型训练-001', modelType: '科创健康性模型', status: 'COMPLETED' },
      { id: '2', name: '内控风险模型训练-002', modelType: '内控风险模型', status: 'RUNNING' },
      { id: '3', name: '科创健康性模型训练-003', modelType: '科创健康性模型', status: 'FAILED' },
      { id: '4', name: '内控风险模型训练-004', modelType: '内控风险模型', status: 'PENDING' },
      { id: '5', name: '科创健康性模型训练-005', modelType: '科创健康性模型', status: 'COMPLETED' },
    ];

    loading.value = false;
  }, 1000);
});
</script>

<style lang="less" scoped>
.dashboard-container {
  padding: 24px;
}

.resource-label {
  margin-top: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
