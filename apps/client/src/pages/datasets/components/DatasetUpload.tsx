import { defineComponent, ref, reactive } from 'vue';
import {
  Modal,
  Upload,
  Form,
  Input,
  Select,
  Button,
  message,
  Steps,
  Card,
  Typography,
  Row,
  Col,
  Progress,
} from 'ant-design-vue';
import {
  InboxOutlined,
  CloudUploadOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
} from '@ant-design/icons-vue';
import { useDatasetStore } from '@/store/modules/dataset.store';

const { Title, Text } = Typography;
const { Option } = Select;
const { Step } = Steps;
const { Dragger } = Upload;

export default defineComponent({
  name: 'DatasetUpload',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const datasetStore = useDatasetStore();
    const currentStep = ref(0);
    const uploading = ref(false);
    const uploadProgress = ref(0);

    const formData = reactive({
      name: '',
      description: '',
      modelType: '',
      tags: [],
    });

    const fileList = ref([]);

    // 模型类型选项
    const modelTypes = [
      { value: 'TECH_HEALTH', label: '科创健康性模型' },
      { value: 'INTERNAL_CONTROL_RISK', label: '内控风险模型' },
      { value: 'CREDIT_RISK', label: '信用风险模型' },
      { value: 'MARKET_RISK', label: '市场风险模型' },
    ];

    const handleClose = () => {
      emit('update:visible', false);
      resetForm();
    };

    const resetForm = () => {
      currentStep.value = 0;
      uploading.value = false;
      uploadProgress.value = 0;
      Object.assign(formData, {
        name: '',
        description: '',
        modelType: '',
        tags: [],
      });
      fileList.value = [];
    };

    const handleNext = () => {
      if (currentStep.value === 0 && fileList.value.length === 0) {
        message.error('请先选择文件');
        return;
      }
      if (currentStep.value === 1) {
        if (!formData.name || !formData.modelType) {
          message.error('请填写必要信息');
          return;
        }
      }
      currentStep.value++;
    };

    const handlePrev = () => {
      currentStep.value--;
    };

    const handleUpload = async () => {
      if (fileList.value.length === 0) {
        message.error('请选择文件');
        return;
      }

      uploading.value = true;
      uploadProgress.value = 0;

      try {
        const formDataToSend = new FormData();
        formDataToSend.append('file', fileList.value[0].originFileObj);
        formDataToSend.append('name', formData.name);
        formDataToSend.append('description', formData.description);
        formDataToSend.append('modelType', formData.modelType);
        formDataToSend.append('tags', JSON.stringify(formData.tags));

        // 模拟上传进度
        const progressInterval = setInterval(() => {
          uploadProgress.value += 10;
          if (uploadProgress.value >= 90) {
            clearInterval(progressInterval);
          }
        }, 200);

        await datasetStore.createDataset(formDataToSend);
        
        clearInterval(progressInterval);
        uploadProgress.value = 100;
        
        setTimeout(() => {
          currentStep.value++;
          emit('success');
        }, 500);
      } catch (error) {
        message.error('上传失败：' + error.message);
      } finally {
        uploading.value = false;
      }
    };

    const uploadProps = {
      name: 'file',
      multiple: false,
      accept: '.csv,.xlsx,.xls',
      beforeUpload: (file) => {
        const isValidType = file.type === 'text/csv' || 
          file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          file.type === 'application/vnd.ms-excel';
        
        if (!isValidType) {
          message.error('只支持 CSV 和 Excel 文件格式');
          return false;
        }

        const isLt100M = file.size / 1024 / 1024 < 100;
        if (!isLt100M) {
          message.error('文件大小不能超过 100MB');
          return false;
        }

        fileList.value = [file];
        return false; // 阻止自动上传
      },
      onRemove: () => {
        fileList.value = [];
      },
    };

    return () => (
      <Modal
        title="上传数据集"
        visible={props.visible}
        onCancel={handleClose}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Steps current={currentStep.value} class="mb-6">
          <Step title="选择文件" icon={<CloudUploadOutlined />} />
          <Step title="填写信息" icon={<InboxOutlined />} />
          <Step title="上传确认" icon={uploading.value ? <LoadingOutlined /> : undefined} />
          <Step title="完成" icon={<CheckCircleOutlined />} />
        </Steps>

        {/* 步骤1：选择文件 */}
        {currentStep.value === 0 && (
          <Card>
            <Dragger {...uploadProps} fileList={fileList.value}>
              <p class="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持 CSV、Excel 格式，文件大小不超过 100MB
              </p>
            </Dragger>
            <div class="mt-4 text-right">
              <Button type="primary" onClick={handleNext} disabled={fileList.value.length === 0}>
                下一步
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤2：填写信息 */}
        {currentStep.value === 1 && (
          <Card>
            <Form layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="数据集名称" required>
                    <Input
                      value={formData.name}
                      onChange={(e) => (formData.name = e.target.value)}
                      placeholder="请输入数据集名称"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="模型类型" required>
                    <Select
                      value={formData.modelType}
                      onChange={(value) => (formData.modelType = value)}
                      placeholder="请选择模型类型"
                    >
                      {modelTypes.map((type) => (
                        <Option key={type.value} value={type.value}>
                          {type.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item label="描述">
                <Input.TextArea
                  value={formData.description}
                  onChange={(e) => (formData.description = e.target.value)}
                  placeholder="请输入数据集描述"
                  rows={4}
                />
              </Form.Item>
            </Form>
            <div class="text-right">
              <Button onClick={handlePrev} class="mr-2">
                上一步
              </Button>
              <Button type="primary" onClick={handleNext}>
                下一步
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤3：上传确认 */}
        {currentStep.value === 2 && (
          <Card>
            <div class="mb-4">
              <Title level={4}>确认上传信息</Title>
            </div>
            <Row gutter={16}>
              <Col span={12}>
                <div class="mb-2">
                  <Text strong>文件名：</Text>
                  <Text>{fileList.value[0]?.name}</Text>
                </div>
                <div class="mb-2">
                  <Text strong>数据集名称：</Text>
                  <Text>{formData.name}</Text>
                </div>
                <div class="mb-2">
                  <Text strong>模型类型：</Text>
                  <Text>{modelTypes.find(t => t.value === formData.modelType)?.label}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div class="mb-2">
                  <Text strong>文件大小：</Text>
                  <Text>{(fileList.value[0]?.size / 1024 / 1024).toFixed(2)} MB</Text>
                </div>
                <div class="mb-2">
                  <Text strong>描述：</Text>
                  <Text>{formData.description || '无'}</Text>
                </div>
              </Col>
            </Row>
            
            {uploading.value && (
              <div class="mt-4">
                <Progress percent={uploadProgress.value} status="active" />
                <Text type="secondary">正在上传...</Text>
              </div>
            )}
            
            <div class="mt-4 text-right">
              <Button onClick={handlePrev} disabled={uploading.value} class="mr-2">
                上一步
              </Button>
              <Button type="primary" onClick={handleUpload} loading={uploading.value}>
                开始上传
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤4：完成 */}
        {currentStep.value === 3 && (
          <Card class="text-center">
            <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
            <div class="mt-4">
              <Title level={3}>上传成功！</Title>
              <Text type="secondary">数据集已成功上传，系统正在处理中...</Text>
            </div>
            <div class="mt-6">
              <Button type="primary" onClick={handleClose}>
                完成
              </Button>
            </div>
          </Card>
        )}
      </Modal>
    );
  },
});
