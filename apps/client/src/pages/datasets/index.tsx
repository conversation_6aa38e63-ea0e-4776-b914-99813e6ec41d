import { defineComponent, ref, onMounted, computed } from 'vue';
import { Card, Table, Button, Input, Select, Space, Tag, Tooltip, Modal, Row, Col, Typography } from 'ant-design-vue';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  DatabaseOutlined,
  FileTextOutlined,
} from '@ant-design/icons-vue';
import { useDatasetStore } from '@/store/modules/dataset.store';
import DatasetUpload from './components/DatasetUpload';
import DatasetPreview from './components/DatasetPreview';

const { Title, Text } = Typography;
const { Option } = Select;

export default defineComponent({
  name: 'DatasetsPage',
  setup() {
    const datasetStore = useDatasetStore();
    const uploadModalVisible = ref(false);
    const detailModalVisible = ref(false);
    const previewModalVisible = ref(false);
    const selectedDataset = ref(null);
    const searchText = ref('');
    const selectedModelType = ref('');

    // 表格列定义
    const columns = [
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        ellipsis: true,
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        width: 250,
        ellipsis: true,
      },
      {
        title: '模型类型',
        dataIndex: 'modelType',
        key: 'modelType',
        width: 120,
      },
      {
        title: '文件大小',
        dataIndex: 'fileSize',
        key: 'fileSize',
        width: 100,
      },
      {
        title: '行数',
        dataIndex: 'rowCount',
        key: 'rowCount',
        width: 80,
      },
      {
        title: '列数',
        dataIndex: 'columnCount',
        key: 'columnCount',
        width: 80,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 150,
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right',
      },
    ];

    // 模型类型选项
    const modelTypes = [
      { value: 'innovation_health_model', label: '科创健康性模型' },
      { value: 'generic_risk_model', label: '内控风险模型' },
    ];

    // 计算属性
    const filteredDatasets = computed(() => {
      let datasets = datasetStore.datasets;

      if (searchText.value) {
        datasets = datasets.filter(
          (dataset) =>
            dataset.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
            dataset.description.toLowerCase().includes(searchText.value.toLowerCase())
        );
      }

      if (selectedModelType.value) {
        datasets = datasets.filter((dataset) => dataset.modelType === selectedModelType.value);
      }

      return datasets;
    });

    // 方法
    const handleSearch = () => {
      fetchDatasets();
    };

    const handleReset = () => {
      searchText.value = '';
      selectedModelType.value = '';
      fetchDatasets();
    };

    const fetchDatasets = async () => {
      try {
        await datasetStore.fetchDatasets(datasetStore.currentPage, datasetStore.pageSize, {
          search: searchText.value,
          modelType: selectedModelType.value,
        });
      } catch (error) {
        console.error('获取数据集列表失败:', error);
      }
    };

    const showUploadModal = () => {
      uploadModalVisible.value = true;
    };

    const showDetailModal = (dataset) => {
      selectedDataset.value = dataset;
      detailModalVisible.value = true;
    };

    const showPreviewModal = (dataset) => {
      selectedDataset.value = dataset;
      previewModalVisible.value = true;
    };

    const handleEdit = (dataset) => {
      // TODO: 实现编辑功能
      console.log('编辑数据集:', dataset);
    };

    const handleDelete = (dataset) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除数据集 "${dataset.name}" 吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            await datasetStore.deleteDataset(dataset.id);
            await fetchDatasets();
          } catch (error) {
            console.error('删除数据集失败:', error);
          }
        },
      });
    };

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const getStatusColor = (status) => {
      const statusMap = {
        READY: 'success',
        PROCESSING: 'processing',
        ERROR: 'error',
        PENDING: 'warning',
      };
      return statusMap[status] || 'default';
    };

    const getStatusText = (status) => {
      const statusMap = {
        READY: '就绪',
        PROCESSING: '处理中',
        ERROR: '错误',
        PENDING: '待处理',
      };
      return statusMap[status] || status;
    };

    // 生命周期
    onMounted(() => {
      fetchDatasets();
    });

    return () => (
      <div class="p-6">
        <div class="mb-6">
          <Title level={2}>
            <DatabaseOutlined class="mr-2" />
            数据集管理
          </Title>
          <Text type="secondary">管理机器学习训练数据集，支持多种模型类型的数据验证和预览</Text>
        </div>

        {/* 搜索和筛选区域 */}
        <Card class="mb-4">
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Input
                value={searchText.value}
                onChange={(e) => (searchText.value = e.target.value)}
                placeholder="搜索数据集名称或描述"
                prefix={<SearchOutlined />}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                value={selectedModelType.value}
                onChange={(value) => (selectedModelType.value = value)}
                placeholder="选择模型类型"
                allowClear
                style={{ width: '100%' }}
              >
                {modelTypes.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" onClick={handleSearch} icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
            <Col span={8} class="text-right">
              <Button type="primary" onClick={showUploadModal} icon={<PlusOutlined />}>
                上传数据集
              </Button>
            </Col>
          </Row>
        </Card>

        {/* 数据集列表 */}
        <Card>
          <Table
            dataSource={filteredDatasets.value}
            columns={columns}
            loading={datasetStore.loading}
            pagination={{
              current: datasetStore.currentPage,
              pageSize: datasetStore.pageSize,
              total: datasetStore.totalCount,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                datasetStore.fetchDatasets(page, pageSize, {
                  search: searchText.value,
                  modelType: selectedModelType.value,
                });
              },
            }}
            scroll={{ x: 1200 }}
            scopedSlots={{
              fileSize: (text) => formatFileSize(text),
              status: (text) => <Tag color={getStatusColor(text)}>{getStatusText(text)}</Tag>,
              createdAt: (text) => new Date(text).toLocaleString(),
              action: (text, record) => (
                <Space>
                  <Tooltip title="查看详情">
                    <Button type="link" size="small" icon={<EyeOutlined />} onClick={() => showDetailModal(record)} />
                  </Tooltip>
                  <Tooltip title="编辑">
                    <Button type="link" size="small" icon={<EditOutlined />} onClick={() => handleEdit(record)} />
                  </Tooltip>
                  <Tooltip title="删除">
                    <Button type="link" size="small" danger icon={<DeleteOutlined />} onClick={() => handleDelete(record)} />
                  </Tooltip>
                </Space>
              ),
            }}
          />
        </Card>

        {/* 上传数据集组件 */}
        <DatasetUpload
          visible={uploadModalVisible.value}
          onUpdate:visible={(visible) => (uploadModalVisible.value = visible)}
          onSuccess={() => {
            uploadModalVisible.value = false;
            fetchDatasets();
          }}
        />

        {/* 数据集详情模态框 */}
        <Modal
          title="数据集详情"
          visible={detailModalVisible.value}
          onCancel={() => (detailModalVisible.value = false)}
          footer={null}
          width={800}
        >
          {selectedDataset.value && (
            <div>
              <Row gutter={16}>
                <Col span={12}>
                  <div class="mb-4">
                    <Text strong>名称：</Text>
                    <Text>{selectedDataset.value.name}</Text>
                  </div>
                  <div class="mb-4">
                    <Text strong>描述：</Text>
                    <Text>{selectedDataset.value.description}</Text>
                  </div>
                  <div class="mb-4">
                    <Text strong>模型类型：</Text>
                    <Text>{selectedDataset.value.modelType}</Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div class="mb-4">
                    <Text strong>文件大小：</Text>
                    <Text>{formatFileSize(selectedDataset.value.fileSize)}</Text>
                  </div>
                  <div class="mb-4">
                    <Text strong>行数：</Text>
                    <Text>{selectedDataset.value.rowCount}</Text>
                  </div>
                  <div class="mb-4">
                    <Text strong>列数：</Text>
                    <Text>{selectedDataset.value.columnCount}</Text>
                  </div>
                </Col>
              </Row>
              <div class="mt-4">
                <Button
                  type="primary"
                  icon={<FileTextOutlined />}
                  onClick={() => {
                    detailModalVisible.value = false;
                    showPreviewModal(selectedDataset.value);
                  }}
                >
                  查看数据预览
                </Button>
              </div>
            </div>
          )}
        </Modal>

        {/* 数据集预览组件 */}
        <DatasetPreview
          visible={previewModalVisible.value}
          dataset={selectedDataset.value}
          onUpdate:visible={(visible) => (previewModalVisible.value = visible)}
        />
      </div>
    );
  },
});
