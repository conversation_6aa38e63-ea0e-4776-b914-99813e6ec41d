<template>
  <div class="datasets-container">
    <a-row :gutter="[16, 16]">
      <a-col :span="24">
        <a-card title="数据集管理" :bordered="false">
          <template #extra>
            <a-button type="primary" @click="showUploadModal">
              <template #icon><upload-outlined /></template>
              上传数据集
            </a-button>
          </template>

          <a-row :gutter="[16, 16]">
            <a-col :span="24">
              <a-form layout="inline" :model="filterForm">
                <a-form-item label="数据集名称">
                  <a-input v-model:value="filterForm.name" placeholder="输入数据集名称" allow-clear />
                </a-form-item>
                <a-form-item label="模型类型">
                  <a-select v-model:value="filterForm.modelType" style="width: 200px" placeholder="选择模型类型" allow-clear>
                    <a-select-option value="innovation_health_model">科创健康性模型</a-select-option>
                    <a-select-option value="generic_risk_model">内控风险模型</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item>
                  <a-button type="primary" @click="handleSearch">
                    <template #icon><search-outlined /></template>
                    搜索
                  </a-button>
                  <a-button style="margin-left: 8px" @click="resetFilters">
                    <template #icon><clear-outlined /></template>
                    重置
                  </a-button>
                </a-form-item>
              </a-form>
            </a-col>
          </a-row>

          <a-table
            :columns="columns"
            :data-source="datasets"
            :loading="loading"
            :pagination="{
              current: currentPage,
              pageSize: pageSize,
              total: totalCount,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
              onChange: handlePageChange,
              onShowSizeChange: handlePageSizeChange,
            }"
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <a @click="showDatasetDetail(record)">{{ record.name }}</a>
              </template>
              <template v-if="column.key === 'modelType'">
                <a-tag :color="getModelTypeColor(record.modelType)">
                  {{ getModelTypeName(record.modelType) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'fileSize'">
                {{ formatFileSize(record.fileSize) }}
              </template>
              <template v-if="column.key === 'createdAt'">
                {{ formatDate(record.createdAt) }}
              </template>
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusName(record.status) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a @click="showDatasetDetail(record)">查看</a>
                  <a-divider type="vertical" />
                  <a-popconfirm
                    title="确定要删除这个数据集吗？"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="handleDelete(record)"
                  >
                    <a class="text-danger">删除</a>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 上传数据集对话框 -->
    <dataset-upload-modal v-model:visible="uploadModalVisible" @success="handleUploadSuccess" />

    <!-- 数据集详情对话框 -->
    <dataset-detail-modal v-model:visible="detailModalVisible" :dataset-id="currentDatasetId" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useDatasetStore } from '@/store/modules/dataset.store';
import { UploadOutlined, SearchOutlined, ClearOutlined } from '@ant-design/icons-vue';
import DatasetUploadModal from './components/DatasetUploadModal.vue';
import DatasetDetailModal from './components/DatasetDetailModal.vue';
import { formatDate } from '@/utils/date.formatter';

const datasetStore = useDatasetStore();

// 状态
const loading = computed(() => datasetStore.loading);
const datasets = computed(() => datasetStore.datasets);
const totalCount = computed(() => datasetStore.totalCount);
const currentPage = ref(1);
const pageSize = ref(10);

// 模态框状态
const uploadModalVisible = ref(false);
const detailModalVisible = ref(false);
const currentDatasetId = ref('');

// 筛选表单
const filterForm = reactive({
  name: '',
  modelType: undefined,
});

// 表格列定义
const columns = [
  {
    title: '数据集名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
  },
  {
    title: '模型类型',
    dataIndex: 'modelType',
    key: 'modelType',
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    sorter: (a, b) => a.fileSize - b.fileSize,
  },
  {
    title: '行数',
    dataIndex: 'rowCount',
    key: 'rowCount',
    sorter: (a, b) => a.rowCount - b.rowCount,
  },
  {
    title: '列数',
    dataIndex: 'columnCount',
    key: 'columnCount',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
  },
];

// 生命周期钩子
onMounted(() => {
  loadDatasets();
});

// 方法
const loadDatasets = async () => {
  const filters = {
    ...filterForm,
    page: currentPage.value,
    pageSize: pageSize.value,
  };
  await datasetStore.fetchDatasets(currentPage.value, pageSize.value, filters);
};

const handleSearch = () => {
  currentPage.value = 1;
  loadDatasets();
};

const resetFilters = () => {
  filterForm.name = '';
  filterForm.modelType = undefined;
  currentPage.value = 1;
  loadDatasets();
};

const handlePageChange = (page: number, pageSize: number) => {
  currentPage.value = page;
  loadDatasets();
};

const handlePageSizeChange = (current: number, size: number) => {
  pageSize.value = size;
  loadDatasets();
};

const showUploadModal = () => {
  uploadModalVisible.value = true;
};

const handleUploadSuccess = () => {
  loadDatasets();
};

const showDatasetDetail = (record) => {
  currentDatasetId.value = record.id;
  detailModalVisible.value = true;
};

const handleDelete = async (record) => {
  try {
    await datasetStore.deleteDataset(record.id);
    loadDatasets();
  } catch (error) {
    console.error('删除数据集失败', error);
  }
};

// 辅助函数
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

const getModelTypeName = (type: string) => {
  const types = {
    health: '科创健康性模型',
    risk: '内控风险模型',
  };
  return types[type] || type;
};

const getModelTypeColor = (type: string) => {
  const colors = {
    health: 'green',
    risk: 'orange',
  };
  return colors[type] || 'blue';
};

const getStatusName = (status: string) => {
  const statuses = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
  };
  return statuses[status] || status;
};

const getStatusColor = (status: string) => {
  const colors = {
    pending: 'blue',
    processing: 'processing',
    completed: 'success',
    failed: 'error',
  };
  return colors[status] || 'default';
};
</script>

<style scoped>
.datasets-container {
  padding: 16px;
}

.text-danger {
  color: #ff4d4f;
}
</style>
