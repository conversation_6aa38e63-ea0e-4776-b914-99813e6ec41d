import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
import axios from 'axios';

interface Dataset {
  id: string;
  name: string;
  description: string;
  modelType: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  fileSize: number;
  rowCount: number;
  columnCount: number;
  schema: any;
  tags: string[];
  status: string;
}

interface DatasetState {
  datasets: Dataset[];
  currentDataset: Dataset | null;
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  filters: Record<string, any>;
}

export const useDatasetStore = defineStore('dataset', {
  state: (): DatasetState => ({
    datasets: [],
    currentDataset: null,
    loading: false,
    error: null,
    totalCount: 0,
    currentPage: 1,
    pageSize: 10,
    filters: {},
  }),

  getters: {
    getDatasetById: (state) => (id: string) => {
      return state.datasets.find((dataset) => dataset.id === id);
    },
  },

  actions: {
    async fetchDatasets(page = 1, pageSize = 10, filters = {}) {
      this.loading = true;
      this.error = null;
      this.currentPage = page;
      this.pageSize = pageSize;
      this.filters = filters;

      try {
        const response = await axios.get('/api/datasets', {
          params: {
            page,
            pageSize,
            ...filters,
          },
        });

        this.datasets = response.data.items;
        this.totalCount = response.data.totalCount;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取数据集列表失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchDatasetById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/datasets/${id}`);
        this.currentDataset = response.data;
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取数据集详情失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async createDataset(formData: FormData) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post('/api/datasets', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        message.success('数据集创建成功');
        return response.data;
      } catch (error: any) {
        this.error = error.message || '创建数据集失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateDataset(id: string, data: Partial<Dataset>) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.put(`/api/datasets/${id}`, data);
        message.success('数据集更新成功');

        // 更新本地数据
        if (this.currentDataset && this.currentDataset.id === id) {
          this.currentDataset = { ...this.currentDataset, ...response.data };
        }

        const index = this.datasets.findIndex((dataset) => dataset.id === id);
        if (index !== -1) {
          this.datasets[index] = { ...this.datasets[index], ...response.data };
        }

        return response.data;
      } catch (error: any) {
        this.error = error.message || '更新数据集失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteDataset(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await axios.delete(`/api/datasets/${id}`);
        message.success('数据集删除成功');

        // 更新本地数据
        if (this.currentDataset && this.currentDataset.id === id) {
          this.currentDataset = null;
        }

        this.datasets = this.datasets.filter((dataset) => dataset.id !== id);

        return true;
      } catch (error: any) {
        this.error = error.message || '删除数据集失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getDatasetPreview(id: string, limit = 100) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/datasets/${id}/preview`, {
          params: { limit },
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取数据集预览失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async validateDataset(id: string, modelType: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post(`/api/datasets/${id}/validate`, {
          modelType,
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '验证数据集失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async getDatasetSchema(modelType: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/datasets/schema`, {
          params: { modelType },
        });
        return response.data;
      } catch (error: any) {
        this.error = error.message || '获取数据集模式失败';
        message.error(this.error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
