import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UploadedFile,
  UseInterceptors
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { UserModel } from '../auth/dto/UserModel';
import { DatasetsService } from './datasets.service';
import { CreateDatasetDto } from './dto/create-dataset.dto';
import { DatasetFiltersDto } from './dto/dataset-filters.dto';
import { SearchDatasetItemDto } from './dto/search-dataset-item.dto';
import { UpdateDatasetDto } from './dto/update-dataset.dto';

@ApiTags('datasets')
@Controller('datasets')
export class DatasetsController {
  constructor(private readonly datasetsService: DatasetsService) {}

  @Post()
  @ApiOperation({ summary: '创建数据集' })
  async create(@Body() createDatasetDto: CreateDatasetDto, @Req() req: Request) {
    const user = req['user'] as UserModel;
    return this.datasetsService.create(createDatasetDto, user.id.toString());
  }

  @Post('search')
  @ApiOperation({ summary: '获取数据集列表' })
  async findAll(@Body() filters: DatasetFiltersDto) {
    const [datasets, total] = await this.datasetsService.findAll(filters);
    return {
      data: datasets,
      total,
      page: filters.skip ? Math.floor(filters.skip / (filters.take || 10)) + 1 : 1,
      pageSize: filters.take || 10,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '获取数据集详情' })
  async findOne(@Param('id') id: string) {
    return this.datasetsService.findOne(id);
  }

  @Post(':id/update')
  @ApiOperation({ summary: '更新数据集' })
  async update(@Param('id') id: string, @Body() updateDatasetDto: UpdateDatasetDto) {
    return this.datasetsService.update(id, updateDatasetDto);
  }

  @Post(':id/delete')
  @ApiOperation({ summary: '删除数据集' })
  async remove(@Param('id') id: string) {
    await this.datasetsService.remove(id);
    return { success: true };
  }

  @Post(':id/items')
  @ApiOperation({ summary: '获取数据集预览' })
  async getPreview(@Param('id') id: string, @Body() body: SearchDatasetItemDto) {
    return this.datasetsService.searchDatasetItems(id, body);
  }

  @Post(':id/upload')
  @ApiOperation({ summary: '上传数据集文件' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/temp',
        filename: (req, file, cb) => {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          return cb(null, `${randomName}${extname(file.originalname)}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.(csv|CSV)$/)) {
          return cb(new BadRequestException('只支持CSV文件'), false);
        }
        cb(null, true);
      },
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB
      },
    })
  )
  async uploadFile(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Req() req: Request,
    @Query('badCase') badCase: string
  ) {
    if (!file) {
      throw new BadRequestException('文件不能为空');
    }

    const user = req['user'] as UserModel;
    return this.datasetsService.processUploadedFile(id, file, user.id.toString(), badCase);
  }

  @Get('schema/:modelType')
  @ApiOperation({ summary: '获取数据集模式' })
  async getDatasetSchema(@Param('modelType') modelType: string) {
    return this.datasetsService.getDatasetSchema(modelType);
  }
}
