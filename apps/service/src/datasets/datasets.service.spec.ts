import { Test, TestingModule } from '@nestjs/testing';
import { DatasetsService } from './datasets.service';
import { getEntityManagerToken, TypeOrmModule } from '@nestjs/typeorm';
import { StorageService } from 'src/common/storage.service';
import { DatasetEntity, HealthModelDatasetEntity, RiskModelDatasetEntity } from 'src/entities';
import { DatasetsController } from './datasets.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import configuration from 'src/config/configuration';
import { EntityManager } from 'typeorm';

describe('DatasetsService', () => {
  let service: DatasetsService;
  let entityManager: EntityManager;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [configuration],
          isGlobal: true,
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          //@ts-ignore
          useFactory: (configService: ConfigService) => configService.get<TypeOrmModuleOptions>('typeorm'),
        }),
        TypeOrmModule.forFeature([DatasetEntity, HealthModelDatasetEntity, RiskModelDatasetEntity]),
      ],
      controllers: [DatasetsController],
      providers: [DatasetsService, StorageService],
      exports: [DatasetsService],
    }).compile();

    service = module.get<DatasetsService>(DatasetsService);
    entityManager = module.get<EntityManager>(getEntityManagerToken());
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('exportDatasetToCsv() test', async () => {
    const dataset: DatasetEntity | null = await entityManager.findOne(DatasetEntity, {
      where: {
        id: 'bda6df7f-11f2-4c25-84e9-32bcb747682c',
      },
    });
    const csvPath = await service.exportDatasetToCsv(dataset);
    console.log(csvPath);
    expect(csvPath).toBeDefined();
  });
});
