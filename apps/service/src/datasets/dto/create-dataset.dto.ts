import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

export enum ModelType {
  HEALTH_MODEL = 'health_model',
  RISK_MODEL = 'risk_model',
}

export class CreateDatasetDto {
  @ApiProperty({ description: '数据集名称' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: '数据集描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: '模型类型',
    enum: ModelType,
    example: ModelType.HEALTH_MODEL,
  })
  @IsEnum(ModelType)
  @IsNotEmpty()
  modelType: ModelType;

  @ApiProperty({ description: '标签', required: false, type: [String] })
  @IsOptional()
  tags?: string[];
}
