import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { ModelType } from './create-dataset.dto';
import { DatasetStatus } from '../../entities/DatasetEntity';

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class DatasetFiltersDto {
  @ApiProperty({ description: '模型类型', enum: ModelType, required: false })
  @IsEnum(ModelType)
  @IsOptional()
  modelType?: ModelType;

  @ApiProperty({ description: '数据集状态', enum: DatasetStatus, required: false })
  @IsEnum(DatasetStatus)
  @IsOptional()
  status?: DatasetStatus;

  @ApiProperty({ description: '创建者ID', required: false })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({ description: '排序顺序', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsEnum(SortOrder)
  @IsOptional()
  sortOrder?: SortOrder;

  @ApiProperty({ description: '跳过记录数', required: false })
  @IsNumber()
  @IsOptional()
  skip?: number;

  @ApiProperty({ description: '获取记录数', required: false })
  @IsNumber()
  @IsOptional()
  take?: number;
}
