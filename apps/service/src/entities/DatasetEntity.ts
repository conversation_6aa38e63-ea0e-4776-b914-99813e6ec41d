import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum DatasetStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  READY = 'ready',
  ERROR = 'error',
}

@Entity('datasets')
@Index(['modelType'])
@Index(['createdBy'])
@Index(['status'])
export class DatasetEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({ name: 'model_type', length: 50 })
  modelType: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by', length: 36 })
  createdBy: string;

  @Column({ name: 'file_size', type: 'bigint' })
  fileSize: number;

  @Column({ name: 'row_count', type: 'int' })
  rowCount: number;

  @Column({ name: 'column_count', type: 'int' })
  columnCount: number;

  @Column('json')
  schema: Record<string, any>;

  @Column('json', { nullable: true })
  tags: string[];

  @Column({
    type: 'enum',
    enum: DatasetStatus,
    default: DatasetStatus.UPLOADING,
  })
  status: DatasetStatus;
}
