import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { DatasetEntity } from './DatasetEntity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('health_model_datasets')
@Index(['datasetId'])
export class HealthModelDatasetEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'dataset_id', length: 36 })
  datasetId: string;

  @ManyToOne(() => DatasetEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'dataset_id' })
  dataset: DatasetEntity;

  // 企业基本信息
  /**
   * 企业唯一标识符
   */
  @ApiProperty({ description: '企业唯一标识符' })
  @Column({ name: 'enterprise_id', length: 36, nullable: false })
  enterpriseId: string;

  /**
   * 企业名称
   */
  @ApiProperty({ description: '企业名称' })
  @Column({ name: 'enterprise_name', length: 255, nullable: false })
  enterpriseName: string;

  /**
   * 综合评分
   * 0-100，基于所有指标的加权计算结果，可为null
   */
  @ApiProperty({ description: '综合评分，0-100，基于所有指标的加权计算结果，可为null' })
  @Column({ name: 'comprehensive_score', type: 'float', nullable: true })
  comprehensiveScore: number | null;

  /**
   * 企业好坏标签
   * 0=好企业，1=坏企业，可为null
   */
  @ApiProperty({ description: '企业好坏标签，0=好企业，1=坏企业' })
  @Column({ name: 'label', type: 'int', nullable: true })
  label: number | null;

  // 技术专利指标
  /**
   * 发明专利申请占比
   * 0-1，可能为null
   */
  @ApiProperty({ description: '发明专利申请占比，0-1，可能为null' })
  @Column({ name: 'tech_patent_application_ratio', type: 'float', nullable: true })
  techPatentApplicationRatio: number | null;

  /**
   * 发明专利申请驳回率
   * 0-1，可能为null
   */
  @ApiProperty({ description: '发明专利申请驳回率，0-1，可能为null' })
  @Column({ name: 'tech_patent_rejection_rate', type: 'float', nullable: true })
  techPatentRejectionRate: number | null;

  /**
   * 发明专利授权率
   * 0-1，可能为null
   */
  @ApiProperty({ description: '发明专利授权率，0-1，可能为null' })
  @Column({ name: 'tech_patent_authorization_rate', type: 'float', nullable: true })
  techPatentAuthorizationRate: number | null;

  /**
   * 发明专利授权维持率
   * 0-1，可能为null
   */
  @ApiProperty({ description: '发明专利授权维持率，0-1，可能为null' })
  @Column({ name: 'tech_patent_maintenance_rate', type: 'float', nullable: true })
  techPatentMaintenanceRate: number | null;

  /**
   * 发明专利授权稳定性系数
   * 0-1000，可能为null
   */
  @ApiProperty({ description: '发明专利授权稳定性系数，0-1000，可能为null' })
  @Column({ name: 'tech_patent_authorization_stability', type: 'float', nullable: true })
  techPatentAuthorizationStability: number | null;

  /**
   * 发明专利授权排名百分位
   * 0-1，可能为null
   */
  @ApiProperty({ description: '发明专利授权排名百分位，0-1，可能为null' })
  @Column({ name: 'tech_patent_authorization_ranking', type: 'float', nullable: true })
  techPatentAuthorizationRanking: number | null;

  /**
   * 软件著作权排名百分位
   * 0-1，可能为null
   */
  @ApiProperty({ description: '软件著作权排名百分位，0-1，可能为null' })
  @Column({ name: 'tech_software_copyright_ranking', type: 'float', nullable: true })
  techSoftwareCopyrightRanking: number | null;

  /**
   * 发明专利申请集中度
   * 0-1，可能为null
   */
  @ApiProperty({ description: '发明专利申请集中度，0-1，可能为null' })
  @Column({ name: 'tech_patent_concentration', type: 'float', nullable: true })
  techPatentConcentration: number | null;

  /**
   * 外购专利占比
   * 0-1，可能为null
   */
  @ApiProperty({ description: '外购专利占比，0-1，可能为null' })
  @Column({ name: 'tech_external_patent_ratio', type: 'float', nullable: true })
  techExternalPatentRatio: number | null;

  /**
   * 发明专利申请连续性
   * 0=否，1=是
   */
  @ApiProperty({ description: '发明专利申请连续性，0=否，1=是' })
  @Column({ name: 'tech_patent_continuity', type: 'int', nullable: true })
  techPatentContinuity: number | null;

  /**
   * 发明专利流出
   * 0=否，1=是
   */
  @ApiProperty({ description: '发明专利流出，0=否，1=是' })
  @Column({ name: 'tech_adj_patent_outflow', type: 'int', nullable: true })
  techAdjPatentOutflow: number | null;

  /**
   * 有效PCT国际专利
   * 0=否，1=是
   */
  @ApiProperty({ description: '有效PCT国际专利，0=否，1=是' })
  @Column({ name: 'tech_adj_pct_patent', type: 'int', nullable: true })
  techAdjPctPatent: number | null;

  /**
   * 知识产权质押
   * 0=否，1=是
   */
  @ApiProperty({ description: '知识产权质押，0=否，1=是' })
  @Column({ name: 'tech_adj_ip_pledge', type: 'int', nullable: true })
  techAdjIpPledge: number | null;

  /**
   * 知识产权转化
   * 0=否，1=是
   */
  @ApiProperty({ description: '知识产权转化，0=否，1=是' })
  @Column({ name: 'tech_adj_ip_transformation', type: 'float', nullable: true })
  techAdjIpTransformation: number | null;

  /**
   * 科技成果
   * 0=否，1=是
   */
  @ApiProperty({ description: '科技成果，0=否，1=是' })
  @Column({ name: 'tech_adj_tech_achievement', type: 'int', nullable: true })
  techAdjTechAchievement: number | null;

  // 发展指标
  /**
   * 人才招聘稳定性系数
   * 0-1，可能为null
   */
  @ApiProperty({ description: '人才招聘稳定性系数，0-1，可能为null' })
  @Column({ name: 'dev_talent_stability', type: 'float', nullable: true })
  devTalentStability: number | null;

  /**
   * 股权融资类型
   * 0=无，1=B/C类，2=A类
   */
  @ApiProperty({ description: '股权融资类型，0=无，1=B/C类，2=A类' })
  @Column({ name: 'dev_equity_financing', type: 'int', nullable: true })
  devEquityFinancing: number | null;

  /**
   * 企业荣誉数量
   * 0-1000，可能为null
   */
  @ApiProperty({ description: '企业荣誉数量，0-1000，可能为null' })
  @Column({ name: 'dev_enterprise_honor', type: 'int', nullable: true })
  devEnterpriseHonor: number | null;

  /**
   * 员工持股平台
   * 0=否，1=是
   */
  @ApiProperty({ description: '员工持股平台，0=否，1=是' })
  @Column({ name: 'dev_adj_employee_shareholding', type: 'int', nullable: true })
  devAdjEmployeeShareholding: number | null;

  /**
   * 荣誉取消
   * 0=否，1=是
   */
  @ApiProperty({ description: '荣誉取消，0=否，1=是' })
  @Column({ name: 'dev_adj_honor_cancellation', type: 'int', nullable: true })
  devAdjHonorCancellation: number | null;

  // 运营指标
  /**
   * 注册资本实缴比例
   * 0-1，可能为null
   */
  @ApiProperty({ description: '注册资本实缴比例，0-1，可能为null' })
  @Column({ name: 'oper_capital_paid_ratio', type: 'float', nullable: true })
  operCapitalPaidRatio: number | null;

  /**
   * 关键人员变动次数
   * 0及以上，可能为null，越小越好
   */
  @ApiProperty({ description: '关键人员变动次数，0及以上，可能为null，越小越好' })
  @Column({ name: 'oper_key_personnel_change', type: 'int', nullable: true })
  operKeyPersonnelChange: number | null;

  /**
   * 股权变更次数
   * 0-6，可能为null
   */
  @ApiProperty({ description: '股权变更次数，0-6，可能为null' })
  @Column({ name: 'oper_equity_change_frequency', type: 'int', nullable: true })
  operEquityChangeFrequency: number | null;

  /**
   * 累计减资
   * 0=否，1=是
   */
  @ApiProperty({ description: '累计减资，0=否，1=是' })
  @Column({ name: 'oper_adj_capital_reduction', type: 'int', nullable: true })
  operAdjCapitalReduction: number | null;

  /**
   * 股权结构异常
   * 0=否，1=是
   */
  @ApiProperty({ description: '股权结构异常，0=否，1=是' })
  @Column({ name: 'oper_adj_equity_structure', type: 'int', nullable: true })
  operAdjEquityStructure: number | null;

  /**
   * 关联方异动
   * 0=否，1=是
   */
  @ApiProperty({ description: '关联方异动，0=否，1=是' })
  @Column({ name: 'oper_adj_related_party_change', type: 'float', nullable: true })
  operAdjRelatedPartyChange: number | null;

  /**
   * 主体及关联方经营状态异常
   * 0=否，1=是
   */
  @ApiProperty({ description: '主体及关联方经营状态异常，0=否，1=是' })
  @Column({ name: 'oper_adj_business_status', type: 'int', nullable: true })
  operAdjBusinessStatus: number | null;

  /**
   * 主体及关联方吊销
   * 0=否，1=是
   */
  @ApiProperty({ description: '主体及关联方吊销，0=否，1=是' })
  @Column({ name: 'oper_adj_revocation', type: 'int', nullable: true })
  operAdjRevocation: number | null;

  // 风险指标
  /**
   * 主体及关联方被执行限高
   * 0=否，1=是
   */
  @ApiProperty({ description: '主体及关联方被执行限高，0=否，1=是' })
  @Column({ name: 'risk_adj_execution_restriction', type: 'int', nullable: true })
  riskAdjExecutionRestriction: number | null;

  /**
   * 主体及关联方涉及金融诉讼
   * 0=否，1=是
   */
  @ApiProperty({ description: '主体及关联方涉及金融诉讼，0=否，1=是' })
  @Column({ name: 'risk_adj_financial_litigation', type: 'int', nullable: true })
  riskAdjFinancialLitigation: number | null;

  /**
   * 主体及关联方环保处罚
   * 0=否，1=是
   */
  @ApiProperty({ description: '主体及关联方环保处罚，0=否，1=是' })
  @Column({ name: 'risk_adj_environmental_penalty', type: 'int', nullable: true })
  riskAdjEnvironmentalPenalty: number | null;

  /**
   * 主体及关联方欠税
   * 0=否，1=是
   */
  @ApiProperty({ description: '主体及关联方欠税，0=否，1=是' })
  @Column({ name: 'risk_adj_tax_arrears', type: 'int', nullable: true })
  riskAdjTaxArrears: number | null;

  @CreateDateColumn({ name: 'created_at', comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', comment: '更新时间' })
  updatedAt: Date;
}
