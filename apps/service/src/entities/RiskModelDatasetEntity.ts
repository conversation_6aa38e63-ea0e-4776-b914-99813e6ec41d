import { Entity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, JoinColumn, Index } from 'typeorm';
import { DatasetEntity } from './DatasetEntity';

@Entity('risk_model_datasets')
@Index(['datasetId'])
export class RiskModelDatasetEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'dataset_id', length: 36 })
  datasetId: string;

  @ManyToOne(() => DatasetEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'dataset_id' })
  dataset: DatasetEntity;

  // 企业基本信息
  @Column({ name: 'enterprise_id', length: 36, nullable: true })
  enterpriseId: string;

  @Column({ name: 'enterprise_name', length: 255, nullable: true })
  enterpriseName: string;

  // 风险评估指标
  @Column({ name: 'risk_score', type: 'float', nullable: true })
  riskScore: number | null;

  @Column({ name: 'risk_level', length: 50, nullable: true })
  riskLevel: string;

  @Column({ name: 'financial_health', length: 50, nullable: true })
  financialHealth: string;

  @Column({ name: 'compliance_status', length: 50, nullable: true })
  complianceStatus: string;

  @Column({ name: 'audit_result', length: 100, nullable: true })
  auditResult: string;

  // 其他字段
  @Column('json', { nullable: true })
  additionalFields: Record<string, any>;
}
