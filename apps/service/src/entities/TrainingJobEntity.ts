import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm';
import { DatasetEntity } from './DatasetEntity';
import { TrainingParametersModel } from 'src/common/TrainingParametersModel';

export enum JobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Entity('training_jobs')
@Index(['datasetId'])
@Index(['modelType'])
@Index(['status'])
@Index(['createdBy'])
export class TrainingJobEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({ name: 'dataset_id', length: 36 })
  datasetId: string;

  @ManyToOne(() => DatasetEntity)
  @JoinColumn({ name: 'dataset_id' })
  dataset: DatasetEntity;

  @Column({ name: 'model_type', length: 50 })
  modelType: string;

  @Column('json')
  parameters: TrainingParametersModel;

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.PENDING,
  })
  status: JobStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'started_at', type: 'datetime', nullable: true })
  startedAt: Date;

  @Column({ name: 'completed_at', type: 'datetime', nullable: true })
  completedAt: Date;

  @Column({ name: 'created_by', length: 36 })
  createdBy: string;

  @Column({ name: 'model_id', length: 36, nullable: true })
  modelId: string;
}
