import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { ModelType } from '../../datasets/dto/create-dataset.dto';
import { SortOrder } from '../../datasets/dto/dataset-filters.dto';
import { JobStatus } from '../../entities/TrainingJobEntity';
import { PaginationQueryParams } from 'src/common/PaginationQueryParams';

export class TrainingJobFiltersDto extends PaginationQueryParams {
  @ApiProperty({ description: '模型类型', enum: ModelType, required: false })
  @IsEnum(ModelType)
  @IsOptional()
  modelType?: ModelType;

  @ApiProperty({ description: '训练任务状态', enum: JobStatus, required: false })
  @IsEnum(JobStatus)
  @IsOptional()
  status?: JobStatus;

  @ApiProperty({ description: '数据集ID', required: false })
  @IsUUID()
  @IsOptional()
  datasetId?: string;

  @ApiProperty({ description: '创建者ID', required: false })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({ description: '排序顺序', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsEnum(SortOrder)
  @IsOptional()
  sortOrder?: SortOrder;
}
